import React from "react";
import { Badge } from "@/components/ui/badge";
import { OrderStatus } from "@/types/order";
import { 
  Clock, 
  Package, 
  Truck, 
  CheckCircle, 
  XCircle,
  AlertCircle 
} from "lucide-react";

interface OrderStatusBadgeProps {
  status: OrderStatus;
  showIcon?: boolean;
  size?: "sm" | "md" | "lg";
}

const statusConfig = {
  pending: {
    variant: "warning" as const,
    label: "Pending",
    icon: Clock,
    description: "Order is being processed"
  },
  processing: {
    variant: "info" as const,
    label: "Processing",
    icon: Package,
    description: "Order is being prepared"
  },
  shipped: {
    variant: "purple" as const,
    label: "Shipped",
    icon: Truck,
    description: "Order is on the way"
  },
  delivered: {
    variant: "success" as const,
    label: "Delivered",
    icon: CheckCircle,
    description: "Order has been delivered"
  },
  cancelled: {
    variant: "destructive" as const,
    label: "Cancelled",
    icon: XCircle,
    description: "Order has been cancelled"
  }
};

const OrderStatusBadge: React.FC<OrderStatusBadgeProps> = ({ 
  status, 
  showIcon = true, 
  size = "md" 
}) => {
  const config = statusConfig[status] || {
    variant: "outline" as const,
    label: "Unknown",
    icon: AlertCircle,
    description: "Unknown status"
  };

  const Icon = config.icon;
  
  const sizeClasses = {
    sm: "px-2 py-0.5 text-xs min-h-[24px]",
    md: "px-2.5 py-1 text-xs sm:text-sm min-h-[28px] sm:min-h-[32px]",
    lg: "px-3 py-1 sm:py-1.5 text-sm sm:text-base min-h-[32px] sm:min-h-[36px] max-w-[120px] sm:max-w-none"
  };

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16
  };

  return (
    <Badge
      variant={config.variant}
      className={`${sizeClasses[size]} gap-1 sm:gap-1.5 font-medium flex-shrink-0 whitespace-nowrap`}
      title={config.description}
    >
      {showIcon && <Icon size={iconSizes[size]} className="flex-shrink-0" />}
      <span className="truncate">{config.label}</span>
    </Badge>
  );
};

export default OrderStatusBadge;
