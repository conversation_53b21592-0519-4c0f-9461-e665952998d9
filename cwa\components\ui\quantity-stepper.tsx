"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Minus, Plus, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface QuantityStepperProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  disabled?: boolean;
  loading?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showInput?: boolean;
  debounceMs?: number;
  onValidationError?: (error: string) => void;
}

const QuantityStepper: React.FC<QuantityStepperProps> = ({
  value,
  onChange,
  min = 1,
  max = 99,
  disabled = false,
  loading = false,
  size = 'md',
  className = '',
  showInput = false,
  debounceMs = 300,
  onValidationError,
}) => {
  const [localValue, setLocalValue] = useState(value.toString());
  const [isUpdating, setIsUpdating] = useState(false);

  // Debounced update function
  const debouncedUpdate = useCallback(
    debounce((newValue: number) => {
      if (newValue >= min && newValue <= max) {
        onChange(newValue);
        setIsUpdating(false);
      } else {
        const error = `Quantity must be between ${min} and ${max}`;
        onValidationError?.(error);
        toast.error(error);
        setLocalValue(value.toString());
        setIsUpdating(false);
      }
    }, debounceMs),
    [onChange, min, max, debounceMs, onValidationError, value]
  );

  // Update local value when prop value changes
  useEffect(() => {
    setLocalValue(value.toString());
  }, [value]);

  const sizeClasses = {
    sm: {
      button: 'w-7 h-7 text-sm',
      input: 'w-12 h-7 text-sm px-2',
      display: 'min-w-[2rem] px-2 py-1 text-sm',
      icon: 14,
    },
    md: {
      button: 'w-9 h-9 text-base',
      input: 'w-16 h-9 text-base px-3',
      display: 'min-w-[2.5rem] px-3 py-2 text-base',
      icon: 16,
    },
    lg: {
      button: 'w-11 h-11 text-lg',
      input: 'w-20 h-11 text-lg px-4',
      display: 'min-w-[3rem] px-4 py-3 text-lg',
      icon: 18,
    },
  };

  const handleDecrement = () => {
    if (disabled || loading || value <= min) return;
    
    setIsUpdating(true);
    const newValue = value - 1;
    setLocalValue(newValue.toString());
    debouncedUpdate(newValue);
  };

  const handleIncrement = () => {
    if (disabled || loading || value >= max) return;
    
    setIsUpdating(true);
    const newValue = value + 1;
    setLocalValue(newValue.toString());
    debouncedUpdate(newValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setLocalValue(inputValue);
    
    const numValue = parseInt(inputValue, 10);
    if (!isNaN(numValue)) {
      setIsUpdating(true);
      debouncedUpdate(numValue);
    }
  };

  const handleInputBlur = () => {
    const numValue = parseInt(localValue, 10);
    if (isNaN(numValue) || numValue < min || numValue > max) {
      setLocalValue(value.toString());
      if (!isNaN(numValue)) {
        const error = `Quantity must be between ${min} and ${max}`;
        onValidationError?.(error);
        toast.error(error);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      handleIncrement();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      handleDecrement();
    }
  };

  return (
    <div 
      className={cn(
        'flex items-center border border-gray-200 rounded-md bg-white',
        'focus-within:border-accent focus-within:ring-2 focus-within:ring-accent/20',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      role="group"
      aria-label="Quantity selector"
    >
      {/* Decrease Button */}
      <motion.button
        type="button"
        onClick={handleDecrement}
        disabled={disabled || loading || value <= min}
        className={cn(
          sizeClasses[size].button,
          'flex items-center justify-center text-gray-500 hover:text-accent',
          'hover:bg-gray-50 transition-colors rounded-l-md border-r border-gray-200',
          'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent',
          'focus:outline-none focus:ring-2 focus:ring-accent/50 focus:ring-inset'
        )}
        whileTap={{ scale: disabled || loading || value <= min ? 1 : 0.95 }}
        aria-label="Decrease quantity"
      >
        <AnimatePresence mode="wait">
          {loading && isUpdating ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Loader2 size={sizeClasses[size].icon} className="animate-spin" />
            </motion.div>
          ) : (
            <motion.div
              key="minus"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Minus size={sizeClasses[size].icon} />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Quantity Display/Input */}
      {showInput ? (
        <input
          type="number"
          value={localValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          min={min}
          max={max}
          disabled={disabled || loading}
          className={cn(
            sizeClasses[size].input,
            'text-center border-0 focus:outline-none focus:ring-0',
            'appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
          )}
          aria-label="Quantity"
        />
      ) : (
        <div 
          className={cn(
            sizeClasses[size].display,
            'text-center font-medium border-x border-gray-200 bg-gray-50'
          )}
          aria-live="polite"
          aria-label={`Quantity: ${value}`}
        >
          <AnimatePresence mode="wait">
            <motion.span
              key={value}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.15 }}
            >
              {value}
            </motion.span>
          </AnimatePresence>
        </div>
      )}

      {/* Increase Button */}
      <motion.button
        type="button"
        onClick={handleIncrement}
        disabled={disabled || loading || value >= max}
        className={cn(
          sizeClasses[size].button,
          'flex items-center justify-center text-gray-500 hover:text-accent',
          'hover:bg-gray-50 transition-colors rounded-r-md border-l border-gray-200',
          'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent',
          'focus:outline-none focus:ring-2 focus:ring-accent/50 focus:ring-inset'
        )}
        whileTap={{ scale: disabled || loading || value >= max ? 1 : 0.95 }}
        aria-label="Increase quantity"
      >
        <AnimatePresence mode="wait">
          {loading && isUpdating ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Loader2 size={sizeClasses[size].icon} className="animate-spin" />
            </motion.div>
          ) : (
            <motion.div
              key="plus"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              <Plus size={sizeClasses[size].icon} />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>
    </div>
  );
};

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export default QuantityStepper;
