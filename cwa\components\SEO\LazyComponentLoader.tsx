"use client";

import React, { Suspense, lazy, ComponentType } from "react";
import { motion } from "framer-motion";

interface LazyComponentLoaderProps {
  componentName: string;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  loadingText?: string;
  className?: string;
  delay?: number;
}

// Loading component with skeleton
const LoadingSkeleton = ({ className = "", loadingText = "Loading..." }: { className?: string; loadingText?: string }) => (
  <div className={`animate-pulse ${className}`}>
    <div className="bg-gray-200 rounded-lg h-48 w-full flex items-center justify-center">
      <div className="text-gray-500 text-sm">{loadingText}</div>
    </div>
  </div>
);

// Error boundary component
class LazyLoadErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Lazy component loading error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">Failed to load component</p>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lazy component loader with advanced features
export default function LazyComponentLoader({
  componentName,
  fallback,
  errorFallback,
  loadingText = "Loading component...",
  className = "",
  delay = 0,
}: LazyComponentLoaderProps) {
  // Dynamic import with error handling
  const LazyComponent = lazy(() => 
    new Promise<{ default: ComponentType<any> }>((resolve, reject) => {
      const loadComponent = async () => {
        try {
          // Add artificial delay if specified
          if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }

          // Dynamic import based on component name
          let module;
          switch (componentName) {
            case "ImageGallery":
              module = await import("@/components/ImageGallery");
              break;
            case "ProductCardsSlider":
              module = await import("@/components/ProductCardsSlider");
              break;
            case "VideoBlogGrid":
              module = await import("@/components/VideoBlogGrid");
              break;
            case "YouTubeBlogGrid":
              module = await import("@/components/YouTubeBlogGrid");
              break;
            case "MapComponent":
              module = await import("@/components/MapComponent");
              break;
            case "ProductDetailModal":
              module = await import("@/components/ProductDetailModal");
              break;
            case "ImageZoomModal":
              module = await import("@/components/ImageZoomModal");
              break;
            default:
              // Generic dynamic import
              module = await import(`@/components/${componentName}`);
          }
          
          resolve(module);
        } catch (error) {
          console.error(`Failed to load component ${componentName}:`, error);
          reject(error);
        }
      };

      loadComponent();
    })
  );

  const defaultFallback = fallback || (
    <LoadingSkeleton className={className} loadingText={loadingText} />
  );

  return (
    <LazyLoadErrorBoundary fallback={errorFallback}>
      <Suspense fallback={defaultFallback}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className={className}
        >
          <LazyComponent />
        </motion.div>
      </Suspense>
    </LazyLoadErrorBoundary>
  );
}

// Pre-configured lazy loaders for common components
export const LazyImageGallery = (props: any) => (
  <LazyComponentLoader
    componentName="ImageGallery"
    loadingText="Loading image gallery..."
    {...props}
  />
);

export const LazyProductCardsSlider = (props: any) => (
  <LazyComponentLoader
    componentName="ProductCardsSlider"
    loadingText="Loading products..."
    {...props}
  />
);

export const LazyVideoBlogGrid = (props: any) => (
  <LazyComponentLoader
    componentName="VideoBlogGrid"
    loadingText="Loading video blogs..."
    {...props}
  />
);

export const LazyYouTubeBlogGrid = (props: any) => (
  <LazyComponentLoader
    componentName="YouTubeBlogGrid"
    loadingText="Loading YouTube videos..."
    {...props}
  />
);

export const LazyMapComponent = (props: any) => (
  <LazyComponentLoader
    componentName="MapComponent"
    loadingText="Loading map..."
    delay={500} // Maps can be heavy, add delay
    {...props}
  />
);

export const LazyProductDetailModal = (props: any) => (
  <LazyComponentLoader
    componentName="ProductDetailModal"
    loadingText="Loading product details..."
    {...props}
  />
);

export const LazyImageZoomModal = (props: any) => (
  <LazyComponentLoader
    componentName="ImageZoomModal"
    loadingText="Loading zoom view..."
    {...props}
  />
);

// Hook for lazy loading components with intersection observer
export const useLazyLoad = (threshold = 0.1, rootMargin = "50px") => {
  const [isInView, setIsInView] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  return { ref, isInView };
};

// Component for lazy loading with intersection observer
export const LazyLoadOnScroll: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
}> = ({ 
  children, 
  fallback, 
  className = "", 
  threshold = 0.1, 
  rootMargin = "50px" 
}) => {
  const { ref, isInView } = useLazyLoad(threshold, rootMargin);

  return (
    <div ref={ref} className={className}>
      {isInView ? children : (fallback || <LoadingSkeleton className={className} />)}
    </div>
  );
};

// Utility functions for performance optimization
export const performanceUtils = {
  // Preload component
  preloadComponent: async (componentName: string) => {
    try {
      switch (componentName) {
        case "ImageGallery":
          await import("@/components/ImageGallery");
          break;
        case "ProductCardsSlider":
          await import("@/components/ProductCardsSlider");
          break;
        case "VideoBlogGrid":
          await import("@/components/VideoBlogGrid");
          break;
        case "YouTubeBlogGrid":
          await import("@/components/YouTubeBlogGrid");
          break;
        case "MapComponent":
          await import("@/components/MapComponent");
          break;
        default:
          await import(`@/components/${componentName}`);
      }
      console.log(`Component ${componentName} preloaded successfully`);
    } catch (error) {
      console.warn(`Failed to preload component ${componentName}:`, error);
    }
  },

  // Batch preload multiple components
  preloadComponents: async (componentNames: string[]) => {
    const promises = componentNames.map(name => 
      performanceUtils.preloadComponent(name).catch(err => 
        console.warn(`Failed to preload ${name}:`, err)
      )
    );
    await Promise.allSettled(promises);
  },

  // Check if component is already loaded
  isComponentLoaded: (componentName: string): boolean => {
    try {
      // This is a simplified check - in a real implementation,
      // you might want to maintain a registry of loaded components
      return Boolean(require.cache[require.resolve(`@/components/${componentName}`)]);
    } catch {
      return false;
    }
  },
};

// Hook for preloading components on user interaction
export const usePreloadOnHover = (componentNames: string[]) => {
  const preloadOnHover = React.useCallback(() => {
    performanceUtils.preloadComponents(componentNames);
  }, [componentNames]);

  return { preloadOnHover };
};

// Component wrapper for preloading on hover
export const PreloadOnHover: React.FC<{
  children: React.ReactNode;
  componentNames: string[];
  className?: string;
}> = ({ children, componentNames, className = "" }) => {
  const { preloadOnHover } = usePreloadOnHover(componentNames);

  return (
    <div 
      className={className}
      onMouseEnter={preloadOnHover}
      onFocus={preloadOnHover}
    >
      {children}
    </div>
  );
};
