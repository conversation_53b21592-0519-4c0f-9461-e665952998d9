"use client";

import React, { useState, useEffect } from "react";
import ImageGallery from "./ImageGallery";
import { useMediaQuery } from "@/hooks/useMediaQuery";

interface ResponsiveImageGalleryProps {
  images: string[];
  alt: string;
  className?: string;
  priority?: boolean;
}

const ResponsiveImageGallery: React.FC<ResponsiveImageGalleryProps> = ({
  images,
  alt,
  className = "",
  priority = false,
}) => {
  const [mounted, setMounted] = useState(false);
  
  // Media queries for different breakpoints
  const isMobile = useMediaQuery("(max-width: 767px)");
  const isTablet = useMediaQuery("(min-width: 768px) and (max-width: 1023px)");
  const isDesktop = useMediaQuery("(min-width: 1024px)");
  const isLargeScreen = useMediaQuery("(min-width: 1440px)");

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <div className={`relative aspect-square bg-gray-100 rounded-lg animate-pulse ${className}`}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-gray-400 text-sm">Loading gallery...</div>
        </div>
      </div>
    );
  }

  // Mobile configuration (320px - 767px)
  if (isMobile) {
    return (
      <ImageGallery
        images={images}
        alt={alt}
        className={`w-full ${className}`}
        showThumbnails={images.length > 1}
        showZoom={true}
        autoPlay={false}
        priority={priority}
      />
    );
  }

  // Tablet configuration (768px - 1023px)
  if (isTablet) {
    return (
      <ImageGallery
        images={images}
        alt={alt}
        className={`w-full ${className}`}
        showThumbnails={true}
        showZoom={true}
        autoPlay={false}
        priority={priority}
      />
    );
  }

  // Desktop configuration (1024px - 1439px)
  if (isDesktop) {
    return (
      <ImageGallery
        images={images}
        alt={alt}
        className={`w-full ${className}`}
        showThumbnails={true}
        showZoom={true}
        autoPlay={false}
        priority={priority}
      />
    );
  }

  // Large screen configuration (1440px+)
  if (isLargeScreen) {
    return (
      <ImageGallery
        images={images}
        alt={alt}
        className={`w-full ${className}`}
        showThumbnails={true}
        showZoom={true}
        autoPlay={false}
        priority={priority}
      />
    );
  }

  // Fallback configuration
  return (
    <ImageGallery
      images={images}
      alt={alt}
      className={`w-full ${className}`}
      showThumbnails={true}
      showZoom={true}
      autoPlay={false}
      priority={priority}
    />
  );
};

export default ResponsiveImageGallery;
