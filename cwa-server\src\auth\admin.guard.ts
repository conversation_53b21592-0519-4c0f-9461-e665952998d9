import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Users } from 'src/users/schema/users.schema';
import { IUser } from 'src/users/interfaces/users.interface';
import { Request } from 'express';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    @InjectModel(Users.name) private userModel: Model<IUser>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('No authorization header');
    }

    const [, accessToken] = authHeader.split(' ');

    try {
      const payload = await this.jwtService.verifyAsync(accessToken);

      // If the payload does not contain the expected properties, throw an error
      if (!payload || typeof payload !== 'object' || !payload._id) {
        throw new UnauthorizedException('Invalid accessToken payload');
      }

      // Get user from database to check admin status
      const user = await this.userModel.findById(payload._id).exec();
      
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      if (!user.isAdmin) {
        throw new ForbiddenException('Access denied. Admin privileges required.');
      }

      request['user'] = payload;
      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid or expired accessToken');
    }
  }
}
