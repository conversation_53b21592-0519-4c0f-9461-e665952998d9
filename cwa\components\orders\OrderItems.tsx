import React from "react";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import { CartItem } from "@/types/cart";
import { useCurrency } from "@/contexts/CurrencyContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IMAGE_PLACEHOLDER_URL } from "@/constants/helpers";
import { 
  Package, 
  ExternalLink, 
  AlertTriangle 
} from "lucide-react";

interface OrderItemsProps {
  items: CartItem[];
}

const OrderItems: React.FC<OrderItemsProps> = ({ items }) => {
  const { formatPrice } = useCurrency();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Card>
      <CardHeader className="p-4 sm:p-6 lg:p-8">
        <CardTitle className="flex items-center gap-2 text-base sm:text-lg lg:text-xl">
          <Package size={18} className="flex-shrink-0 lg:w-5 lg:h-5" />
          <span className="truncate">Order Items ({items.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 sm:p-6 lg:p-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-4"
        >
          {items.map((item, index) => {
            // Handle invalid items
            if (!item || !item.product) {
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="flex items-center gap-4 p-4 bg-red-50 border border-red-200 rounded-lg"
                >
                  <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                    <AlertTriangle size={24} className="text-red-500" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-red-900">
                      Product information unavailable
                    </h4>
                    <p className="text-sm text-red-700">
                      This item's data is missing or corrupted
                    </p>
                  </div>
                </motion.div>
              );
            }

            const product = item.product;
            const itemTotal = product.price * item.quantity;

            return (
              <motion.div
                key={product.id}
                variants={itemVariants}
                className="flex flex-col sm:flex-row gap-3 sm:gap-4 lg:gap-6 p-3 sm:p-4 lg:p-6 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
              >
                {/* Product Image */}
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={product.images?.[0] || IMAGE_PLACEHOLDER_URL}
                      alt={product.name || 'Product'}
                      width={96}
                      height={96}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = IMAGE_PLACEHOLDER_URL;
                      }}
                    />
                  </div>
                </div>

                {/* Product Details - Main Content */}
                <div className="flex-1 min-w-0 flex flex-col sm:flex-row sm:items-start gap-3 sm:gap-4 lg:gap-6">
                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-base sm:text-lg lg:text-xl leading-tight truncate">
                      {product.name}
                    </h4>
                    {product.description && (
                      <p className="text-xs sm:text-sm lg:text-base text-muted-foreground mt-1 lg:mt-2 line-clamp-2">
                        {product.description}
                      </p>
                    )}

                    {/* Product Details */}
                    <div className="flex flex-wrap items-center gap-3 sm:gap-4 lg:gap-6 mt-2 sm:mt-3 lg:mt-4 text-xs sm:text-sm lg:text-base text-muted-foreground">
                      <div>
                        <span className="font-medium">Qty:</span> {item.quantity}
                      </div>
                      <div>
                        <span className="font-medium">Unit:</span> {formatPrice(product.price)}
                      </div>
                      {product.category && (
                        <div className="hidden sm:block">
                          <span className="font-medium">Category:</span> {product.category}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Price and Actions */}
                  <div className="flex items-center justify-between sm:flex-col sm:items-end lg:items-center lg:flex-row lg:gap-4 gap-2 pt-2 sm:pt-0 border-t sm:border-t-0 lg:border-t-0 lg:min-w-[200px]">
                    <div className="text-left sm:text-right lg:text-center">
                      <div className="font-bold text-base sm:text-lg lg:text-xl text-accent">
                        {formatPrice(itemTotal)}
                      </div>
                      {item.quantity > 1 && (
                        <div className="text-xs sm:text-sm lg:text-base text-muted-foreground">
                          {formatPrice(product.price)} each
                        </div>
                      )}
                    </div>

                    {/* View Product Link */}
                    {product.id ? (
                      <Link href={`/products/${product.id}`}>
                        <Button variant="outline" size="sm" className="text-xs lg:text-sm min-h-[36px] lg:min-h-[40px] px-3 lg:px-4">
                          <ExternalLink size={12} className="mr-1 lg:mr-2" />
                          <span className="hidden sm:inline">View Product</span>
                          <span className="sm:hidden">View</span>
                        </Button>
                      </Link>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        disabled
                        className="text-xs lg:text-sm min-h-[36px] lg:min-h-[40px] px-3 lg:px-4 opacity-50"
                        title="Product no longer available"
                      >
                        <ExternalLink size={12} className="mr-1 lg:mr-2" />
                        <span className="hidden sm:inline">Unavailable</span>
                        <span className="sm:hidden">N/A</span>
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Items Summary */}
        <div className="mt-4 sm:mt-6 lg:mt-8 pt-3 sm:pt-4 lg:pt-6 border-t">
          <div className="flex justify-between items-center text-sm lg:text-base text-muted-foreground">
            <span>Total Items:</span>
            <span className="font-medium lg:font-semibold lg:text-lg">{items.reduce((sum, item) => sum + item.quantity, 0)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrderItems;
