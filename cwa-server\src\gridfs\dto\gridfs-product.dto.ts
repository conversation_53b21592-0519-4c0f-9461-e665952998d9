import { IsString, <PERSON><PERSON><PERSON>ber, IsBoolean, IsOptional, IsEnum, IsArray, Min, MaxLength } from 'class-validator';

export class GridFSCreateProductDto {
  @IsString()
  @MaxLength(100, { message: 'Product name cannot exceed 100 characters' })
  name: string;

  @IsEnum(['regular', 'featured', 'sale', 'new'], {
    message: 'Type must be one of: regular, featured, sale, new'
  })
  @IsOptional()
  type?: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Min(0, { message: 'Price cannot be negative' })
  price: number;

  @IsString()
  @IsOptional()
  image?: string;

  @IsString()
  @MaxLength(1000, { message: 'Description cannot exceed 1000 characters' })
  description: string;

  @IsNumber({}, { message: 'Quantity must be a number' })
  @Min(0, { message: 'Quantity cannot be negative' })
  quantity: number;

  @IsOptional()
  stock?: boolean | string; // Can be boolean or string from form data

  @IsString()
  category: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[];

  @IsString()
  @IsOptional()
  video?: string;

  @IsString()
  @IsOptional()
  discount?: string;

  @IsString()
  @IsOptional()
  id?: string;
}

export class GridFSUpdateProductDto {
  @IsString()
  @MaxLength(100, { message: 'Product name cannot exceed 100 characters' })
  @IsOptional()
  name?: string;

  @IsEnum(['regular', 'featured', 'sale', 'new'], {
    message: 'Type must be one of: regular, featured, sale, new'
  })
  @IsOptional()
  type?: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Min(0, { message: 'Price cannot be negative' })
  @IsOptional()
  price?: number;

  @IsString()
  @IsOptional()
  image?: string;

  @IsString()
  @MaxLength(1000, { message: 'Description cannot exceed 1000 characters' })
  @IsOptional()
  description?: string;

  @IsNumber({}, { message: 'Quantity must be a number' })
  @Min(0, { message: 'Quantity cannot be negative' })
  @IsOptional()
  quantity?: number;

  @IsOptional()
  stock?: boolean | string; // Can be boolean or string from form data

  @IsString()
  @IsOptional()
  category?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[];

  @IsString()
  @IsOptional()
  video?: string;

  @IsString()
  @IsOptional()
  discount?: string;

  @IsString()
  @IsOptional()
  id?: string;

  // Additional fields for GridFS file handling
  @IsString()
  @IsOptional()
  keepExistingImage?: string;

  @IsString()
  @IsOptional()
  replaceAllImages?: string;
}
