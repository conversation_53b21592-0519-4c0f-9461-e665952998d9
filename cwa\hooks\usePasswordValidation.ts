import { useState, useEffect } from 'react';

export interface PasswordCriteria {
  minLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
}

export interface PasswordValidationResult {
  criteria: PasswordCriteria;
  isValid: boolean;
  score: number; // 0-5 based on how many criteria are met
}

export const usePasswordValidation = (password: string): PasswordValidationResult => {
  const [criteria, setCriteria] = useState<PasswordCriteria>({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecial: false,
  });

  useEffect(() => {
    setCriteria({
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecial: /[^A-Za-z0-9]/.test(password),
    });
  }, [password]);

  const score = Object.values(criteria).filter(Boolean).length;
  const isValid = Object.values(criteria).every(Boolean);

  return {
    criteria,
    isValid,
    score,
  };
};
