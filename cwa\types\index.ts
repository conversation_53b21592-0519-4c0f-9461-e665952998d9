export interface DropdownItem {
  id: string;
  label: string;
  disabled?: boolean;
}

export interface DropDownProps {
  items: DropdownItem[];
  label?: string;
  buttonText: string;
  defaultSelectedItem: DropdownItem;
  onSelectionChange: (selectedItem: DropdownItem) => void;
}

export type HeroType = "view" | "buy";

export interface ProductData {
  id: string;
  type: HeroType;
  video?: string;
  images: string[];
  image?: string;
  title: string;
  category: string;
  available: boolean;
  discount: string;
  price: number;
  description?: string;
  quantity?: number;
  created?: string;
  name?: string;
  material?: string; // Add material property
}

export interface Category {
  id: string;
  name: string;
  image: string[];
}

export interface InfoCardProps {
  title: string;
  desc: string;
  button_text: string;
  link?: string;
}

export interface FeatureCardProps extends InfoCardProps {
  image: string;
}

// Video Blog Types
export interface VideoBlogAuthor {
  id: string;
  name: string;
  email?: string;
}

export interface VideoBlogData {
  id: string;
  title: string;
  description: string;
  // Legacy fields for file-based videos (optional for YouTube videos)
  videoFileId?: string;
  videoFilename?: string;
  mimetype?: string;
  fileSize?: number;
  duration?: number;
  thumbnailFileId?: string;
  thumbnailFilename?: string;
  // New YouTube-specific fields
  youtubeUrl?: string;
  youtubeVideoId?: string;
  category: string;
  tags: string[];
  isActive: boolean;
  views: number;
  videoUrl: string;
  thumbnailUrl: string;
  createdAt: string;
  updatedAt: string;
  author?: VideoBlogAuthor; // Optional author field
}

export interface VideoBlogPagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}
