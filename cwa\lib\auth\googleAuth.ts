// This file contains utility functions for Google authentication

// Note: We're now using the backend for Google authentication
// These functions are kept for compatibility but are no longer used directly

/**
 * Initialize Google Sign-In
 */
export const initGoogleAuth = () => {
  console.log("Google Auth is now handled by the backend");
  return {
    initialized: true,
  };
};

/**
 * Sign in with Google
 * This is now handled by redirecting to the backend endpoint
 */
export const signInWithGoogle = async (): Promise<unknown> => {
  console.warn(
    "signInWithGoogle is deprecated. Use the AuthContext.loginWithGoogle instead."
  );
  throw new Error(
    "Direct Google sign-in is no longer supported. Use AuthContext.loginWithGoogle instead."
  );
};

/**
 * Sign out from Google
 */
export const signOutFromGoogle = async (): Promise<void> => {
  console.warn(
    "signOutFromGoogle is deprecated. Use the AuthContext.logout instead."
  );
  // No action needed as logout is handled by the AuthContext
};

/**
 * Get the current Google user
 */
export const getCurrentGoogleUser = (): unknown | null => {
  console.warn(
    "getCurrentGoogleUser is deprecated. Use the AuthContext.user instead."
  );
  return null;
};
