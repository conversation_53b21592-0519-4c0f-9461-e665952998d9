import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { GridFSBucket, ObjectId } from 'mongodb';
import { Readable } from 'stream';

@Injectable()
export class GridFSService implements OnModuleInit {
  private gridFSBucket: GridFSBucket;

  constructor(@InjectConnection() private connection: Connection) {}

  async onModuleInit() {
    await this.connectGridFS();
  }

  /**
   * Initialize GridFS bucket
   */
  private async connectGridFS(): Promise<boolean> {
    try {
      // Check if MongoDB is connected
      if (this.connection.readyState !== 1) {
        throw new Error('MongoDB not connected');
      }

      // Initialize GridFS bucket
      this.gridFSBucket = new GridFSBucket(this.connection.db, {
        bucketName: 'uploads'
      });

      console.log('GridFS connected successfully');
      return true;
    } catch (error) {
      console.error('GridFS connection error:', error);
      return false;
    }
  }

  /**
   * Get the GridFS bucket
   */
  getGridFSBucket(): GridFSBucket {
    if (!this.gridFSBucket) {
      throw new Error('GridFS bucket not initialized');
    }
    return this.gridFSBucket;
  }

  /**
   * Find a file by filename
   */
  async findFileByFilename(filename: string): Promise<any> {
    try {
      const bucket = this.getGridFSBucket();
      const files = await bucket.find({ filename }).toArray();
      return files.length > 0 ? files[0] : null;
    } catch (error) {
      console.error('Error finding file by filename:', error);
      return null;
    }
  }

  /**
   * Find a file by ID
   */
  async findFileById(id: string): Promise<any> {
    try {
      const bucket = this.getGridFSBucket();
      const files = await bucket.find({ _id: new ObjectId(id) }).toArray();
      return files.length > 0 ? files[0] : null;
    } catch (error) {
      console.error('Error finding file by ID:', error);
      return null;
    }
  }

  /**
   * Delete a file by filename
   */
  async deleteFileByFilename(filename: string): Promise<boolean> {
    try {
      const file = await this.findFileByFilename(filename);
      if (!file) {
        return false;
      }
      
      const bucket = this.getGridFSBucket();
      await bucket.delete(file._id);
      return true;
    } catch (error) {
      console.error('Error deleting file by filename:', error);
      return false;
    }
  }

  /**
   * Delete a file by ID
   */
  async deleteFileById(id: string): Promise<boolean> {
    try {
      const bucket = this.getGridFSBucket();
      await bucket.delete(new ObjectId(id));
      return true;
    } catch (error) {
      console.error('Error deleting file by ID:', error);
      return false;
    }
  }

  /**
   * Create a read stream for a file
   */
  createReadStream(fileId: string): Readable {
    try {
      const bucket = this.getGridFSBucket();
      return bucket.openDownloadStream(new ObjectId(fileId));
    } catch (error) {
      console.error('Error creating read stream:', error);
      throw error;
    }
  }

  /**
   * Create a read stream for a file by filename
   */
  createReadStreamByFilename(filename: string): Readable {
    try {
      const bucket = this.getGridFSBucket();
      return bucket.openDownloadStreamByName(filename);
    } catch (error) {
      console.error('Error creating read stream by filename:', error);
      throw error;
    }
  }
}
