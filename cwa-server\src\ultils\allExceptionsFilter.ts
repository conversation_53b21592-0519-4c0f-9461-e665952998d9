import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    let errorMessage = 'Internal server error';
    let errors: string[] = []; // Unified array for all errors

    if (exception instanceof HttpException) {
      const responseMessage = this.getErrorMessage(exception);
      if (Array.isArray(responseMessage)) {
        errors = responseMessage;
        errorMessage = 'Validation failed';
      } else {
        errors.push(responseMessage);
        errorMessage = responseMessage;
      }
    } else if (exception instanceof Error) {
      errorMessage = exception.message; // For general runtime errors
      errors.push(exception.message); // Add the error to the array
    }

    if (
      exception instanceof Error &&
      exception.message.includes('E11000 duplicate key error')
    ) {
      errorMessage = 'A duplicate entry already exists.';
      errors = ['A duplicate entry already exists.'];
    }

    response.status(status).json({
      meta: {
        statusCode: status,
        status: 'error',
        message: errorMessage,
        errors: errors.length > 0 ? errors : null,
        timestamp: new Date().toISOString(),
        path: request.url,
      },
      data: null, // No data in case of an error
    });
  }

  private getErrorMessage(exception: HttpException): any {
    const response = exception.getResponse();
    if (typeof response === 'string') {
      return response;
    } else if (typeof response === 'object' && 'message' in response) {
      return response['message'];
    }
    return 'Internal server error';
  }
}