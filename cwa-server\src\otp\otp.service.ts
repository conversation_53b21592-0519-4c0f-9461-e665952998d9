import { Injectable } from '@nestjs/common';
import { CreateOtpDto } from './dto/create-otp.dto';
import { UpdateOtpDto } from './dto/update-otp.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Otp } from './schema/otp.schema';
import { Model } from 'mongoose';

@Injectable()
export class OtpService {
  constructor(@InjectModel(Otp.name) private otpModel: Model<Otp>) {}

  async create(createOtpDto: CreateOtpDto) {
    return await this.otpModel.create(createOtpDto);
  }

  async findOne(email: string, otp: string) {
    return await this.otpModel.findOne({ email, otp }).exec();
  }

  async delete(otpId: string) {
    return await this.otpModel.findByIdAndDelete(otpId);
  }
}
