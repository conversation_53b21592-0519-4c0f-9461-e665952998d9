import { Schema, <PERSON>p, SchemaFactory } from '@nestjs/mongoose';
import * as mongoose from 'mongoose';

@Schema({ timestamps: true })
export class Otp extends mongoose.Document {
  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  otp: string;

  @Prop({ type: Date, default: Date.now, expires: 60 * 5 })
  createdAt: Date;
}

export const OtpSchema = SchemaFactory.createForClass(Otp);
