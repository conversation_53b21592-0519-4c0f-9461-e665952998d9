"use client";

import React from "react";
import { Loader2, ImageI<PERSON>, AlertCircle } from "lucide-react";

interface ImageLoadingStateProps {
  isLoading?: boolean;
  hasError?: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

const ImageLoadingState: React.FC<ImageLoadingStateProps> = ({
  isLoading = true,
  hasError = false,
  className = "",
  size = "md",
  showIcon = true,
}) => {
  const sizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg",
  };

  const iconSizes = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  };

  if (hasError) {
    return (
      <div
        className={`absolute inset-0 flex flex-col items-center justify-center bg-gray-100 ${className}`}
        role="alert"
        aria-live="polite"
      >
        {showIcon && (
          <AlertCircle className={`text-red-400 mb-2 ${iconSizes[size]}`} />
        )}
        <div className={`text-red-600 font-medium ${sizeClasses[size]}`}>
          Failed to load image
        </div>
        <div className="text-red-500 text-xs mt-1">
          Please check your connection
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div
        className={`absolute inset-0 flex flex-col items-center justify-center bg-gray-200 animate-pulse ${className}`}
        role="status"
        aria-live="polite"
        aria-label="Loading image"
      >
        {showIcon && (
          <div className="flex items-center space-x-2">
            <Loader2 className={`animate-spin text-gray-400 ${iconSizes[size]}`} />
            <ImageIcon className={`text-gray-400 ${iconSizes[size]}`} />
          </div>
        )}
        <div className={`text-gray-500 mt-2 ${sizeClasses[size]}`}>
          Loading image...
        </div>
        {/* Progress indicator */}
        <div className="w-16 h-1 bg-gray-300 rounded-full mt-2 overflow-hidden">
          <div className="h-full bg-accent rounded-full animate-pulse" />
        </div>
      </div>
    );
  }

  return null;
};

export default ImageLoadingState;
