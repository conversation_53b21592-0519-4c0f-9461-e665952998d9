import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GridFsStorage } from 'multer-gridfs-storage';
import * as path from 'path';
import * as crypto from 'crypto';

@Injectable()
export class MulterConfigService {
  constructor(private configService: ConfigService) {}

  createGridFSStorage() {
    const mongoUri = this.configService.get<string>('MONGODB_URI');
    
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set');
    }

    return new GridFsStorage({
      url: mongoUri,
      options: { useNewUrlParser: true, useUnifiedTopology: true },
      file: (req, file) => {
        return new Promise((resolve, reject) => {
          crypto.randomBytes(16, (err, buf) => {
            if (err) {
              return reject(err);
            }
            
            const fileInfo = {
              filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
              bucketName: 'uploads',
              metadata: {
                originalname: file.originalname,
                mimetype: file.mimetype,
                uploadDate: new Date()
              }
            };
            
            resolve(fileInfo);
          });
        });
      }
    });
  }

  getMulterOptions() {
    const storage = this.createGridFSStorage();

    const fileFilter = (req: any, file: any, cb: any) => {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('Not an image! Please upload only images.'), false);
      }
    };

    return {
      storage: storage,
      fileFilter: fileFilter,
      limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
      }
    };
  }
}
