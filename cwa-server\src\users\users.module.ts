import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { Users, UsersSchema } from './schema/users.schema';
import { AdminGuard } from '../auth/admin.guard';
import { AuthGuard } from '../auth/auth.guard';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: Users.name, schema: UsersSchema }
    ]),
    forwardRef(() => JwtModule),
  ],
  controllers: [UsersController],
  providers: [UsersService, AdminGuard, AuthGuard],
  exports: [UsersService, MongooseModule],
})
export class UsersModule {}