import { Is<PERSON>tring, <PERSON><PERSON><PERSON>ber, IsBoolean, IsOptional, IsEnum, IsArray, Min, MaxLength } from 'class-validator';

export class CreateProductDto {
  @IsString()
  @MaxLength(100, { message: 'Product name cannot exceed 100 characters' })
  name: string;

  @IsEnum(['regular', 'featured', 'sale', 'new'], {
    message: 'Type must be one of: regular, featured, sale, new'
  })
  @IsOptional()
  type?: string;

  @IsNumber({}, { message: 'Price must be a number' })
  @Min(0, { message: 'Price cannot be negative' })
  price: number;

  @IsString()
  @IsOptional()
  image?: string;

  @IsString()
  @MaxLength(1000, { message: 'Description cannot exceed 1000 characters' })
  description: string;

  @IsNumber({}, { message: 'Quantity must be a number' })
  @Min(0, { message: 'Quantity cannot be negative' })
  quantity: number;

  @IsBoolean()
  @IsOptional()
  stock?: boolean;

  @IsString()
  category: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  images?: string[];

  @IsString()
  @IsOptional()
  video?: string;

  @IsString()
  @IsOptional()
  discount?: string;

  @IsString()
  @IsOptional()
  id?: string;
}
