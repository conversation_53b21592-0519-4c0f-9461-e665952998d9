import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  HttpStatus,
  HttpException,
} from '@nestjs/common';

import { ProductsService } from '../products/products.service';
import { GridFSCreateProductDto, GridFSUpdateProductDto } from './dto/gridfs-product.dto';
import { AdminGuard } from '../auth/admin.guard';
import { IProductResponse, IProductQuery } from '../products/interfaces/product.interface';
import { GridFSService } from './gridfs.service';
import { GridFSUploadInterceptor } from './interceptors/gridfs-upload.interceptor';

interface MulterFile {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  size: number;
  filename: string;
  metadata: any;
  bucketName: string;
  chunkSize: number;
  id: any;
}

@Controller('gridfs-products')
export class GridFSProductController {
  constructor(
    private readonly productsService: ProductsService,
    private readonly gridfsService: GridFSService,
  ) {}

  // Public routes
  @Get()
  async getAllProducts(@Query() query: IProductQuery): Promise<IProductResponse> {
    try {
      const result = await this.productsService.findAll(query);
      return {
        status: 'success',
        results: result.results,
        data: {
          products: result.products,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Error fetching products',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getProduct(@Param('id') id: string): Promise<IProductResponse> {
    try {
      const product = await this.productsService.findById(id);
      return {
        status: 'success',
        data: {
          product,
        },
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Error fetching product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Admin-only routes
  @Post()
  @UseGuards(AdminGuard)
  @UseInterceptors(GridFSUploadInterceptor)
  async createProduct(
    @Body() createProductDto: GridFSCreateProductDto,
    @UploadedFiles() files: { image?: MulterFile[], additionalImages?: MulterFile[] },
  ): Promise<IProductResponse> {
    try {
      const productData = { ...createProductDto };

      // Convert string 'true'/'false' values to boolean
      if (productData.stock === 'true' as any) productData.stock = true;
      if (productData.stock === 'false' as any) productData.stock = false;

      // Handle main image upload
      if (files && files.image && files.image.length > 0) {
        productData.image = files.image[0].filename;
      }

      // Handle additional images upload
      if (files && files.additionalImages && files.additionalImages.length > 0) {
        productData.images = files.additionalImages.map((file) => file.filename);
      }

      console.log('Product data to be saved:', productData);

      // Convert to CreateProductDto format
      const createProductData = {
        ...productData,
        stock: typeof productData.stock === 'string' ? productData.stock === 'true' : productData.stock,
      };

      const product = await this.productsService.createProduct(createProductData);

      return {
        status: 'success',
        data: {
          product,
        },
      };
    } catch (error) {
      console.error('Create product error:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to create product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  @UseInterceptors(GridFSUploadInterceptor)
  async updateProduct(
    @Param('id') id: string,
    @Body() updateProductDto: GridFSUpdateProductDto,
    @UploadedFiles() files: { image?: MulterFile[], additionalImages?: MulterFile[] },
  ): Promise<IProductResponse> {
    try {
      // Get existing product first
      const existingProduct = await this.productsService.findById(id);

      const productData = { ...updateProductDto };

      // Convert string 'true'/'false' values to boolean
      if (productData.stock === 'true' as any) productData.stock = true;
      if (productData.stock === 'false' as any) productData.stock = false;

      // Handle main image upload and replacement
      if (files && files.image && files.image.length > 0) {
        // Delete the old image from GridFS if it exists
        if (existingProduct.image) {
          await this.gridfsService.deleteFileByFilename(existingProduct.image);
        }

        // Set the new image
        productData.image = files.image[0].filename;
      } else if (
        (updateProductDto as any).keepExistingImage === 'true' &&
        existingProduct.image
      ) {
        // If keepExistingImage flag is set and there's an existing image, use it
        productData.image = existingProduct.image;
      }

      // Handle additional images upload and replacement
      if (files && files.additionalImages && files.additionalImages.length > 0) {
        // Check if we should replace all images or add to existing ones
        if ((updateProductDto as any).replaceAllImages === 'true') {
          // Delete all existing additional images from GridFS
          if (existingProduct.images && existingProduct.images.length > 0) {
            for (const imageFilename of existingProduct.images) {
              await this.gridfsService.deleteFileByFilename(imageFilename);
            }
          }
          // Set new images
          productData.images = files.additionalImages.map((file) => file.filename);
        } else {
          // Add new images to existing ones
          const existingImages = existingProduct.images || [];
          const newImages = files.additionalImages.map((file) => file.filename);
          productData.images = [...existingImages, ...newImages];
        }
      } else if (existingProduct.images && existingProduct.images.length > 0) {
        // Keep existing additional images if no new ones are uploaded
        productData.images = existingProduct.images;
      }

      // Remove flags from the data to be saved
      delete (productData as any).keepExistingImage;
      delete (productData as any).replaceAllImages;

      console.log('Product data to be saved:', productData);

      // Convert to UpdateProductDto format
      const updateProductData = {
        ...productData,
        stock: typeof productData.stock === 'string' ? productData.stock === 'true' : productData.stock,
      };

      // Remove GridFS-specific fields
      delete updateProductData.keepExistingImage;
      delete updateProductData.replaceAllImages;

      const product = await this.productsService.updateProduct(id, updateProductData);

      return {
        status: 'success',
        data: {
          product,
        },
      };
    } catch (error) {
      console.error('Update product error:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to update product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  async deleteProduct(@Param('id') id: string): Promise<{ status: string; message: string }> {
    try {
      // Get the product first to delete associated images
      const product = await this.productsService.findById(id);

      // Delete main image from GridFS if it exists
      if (product.image) {
        await this.gridfsService.deleteFileByFilename(product.image);
      }

      // Delete additional images from GridFS if they exist
      if (product.images && product.images.length > 0) {
        for (const imageFilename of product.images) {
          await this.gridfsService.deleteFileByFilename(imageFilename);
        }
      }

      // Delete the product
      await this.productsService.deleteProduct(id);

      return {
        status: 'success',
        message: 'Product deleted successfully',
      };
    } catch (error) {
      console.error('Delete product error:', error);
      throw new HttpException(
        {
          status: 'error',
          message: error.message || 'Failed to delete product',
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
