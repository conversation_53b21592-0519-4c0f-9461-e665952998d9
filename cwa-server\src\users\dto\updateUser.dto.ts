import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ptional,
  IsBoolean
} from 'class-validator';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @MaxLength(25, { message: 'Name cannot exceed 25 characters' })
  @Matches(/^[A-Za-z\s]+$/, { message: 'Name can only contain alphabets and spaces' })
  name?: string;

  @IsOptional()
  @IsEmail({}, { message: 'Please enter a valid email' })
  email?: string;

  @IsOptional()
  @IsString()
  @Matches(/^\d{11}$/, { message: 'Phone number must be exactly 11 digits' })
  phone?: string;

  @IsOptional()
  @IsBoolean()
  isAdmin?: boolean;

  // Optional fields for compatibility
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;
}