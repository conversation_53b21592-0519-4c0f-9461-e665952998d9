"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  TrendingUp, 
  Eye, 
  Search,
  Globe,
  Zap,
  BarChart3,
  RefreshCw
} from "lucide-react";

interface SEOMetrics {
  score: number;
  issues: SEOIssue[];
  recommendations: string[];
  performance: PerformanceMetrics;
  accessibility: AccessibilityMetrics;
  seo: SEOChecks;
}

interface SEOIssue {
  type: "error" | "warning" | "info";
  category: string;
  message: string;
  element?: string;
  fix?: string;
}

interface PerformanceMetrics {
  lcp: number | null;
  fid: number | null;
  cls: number | null;
  fcp: number | null;
  ttfb: number | null;
}

interface AccessibilityMetrics {
  score: number;
  issues: number;
  warnings: number;
}

interface SEOChecks {
  metaTags: boolean;
  headingStructure: boolean;
  imageAltText: boolean;
  internalLinks: boolean;
  structuredData: boolean;
  sitemap: boolean;
  robotsTxt: boolean;
}

/**
 * SEO Audit Dashboard Component
 * Provides real-time SEO analysis and recommendations
 */
export default function SEOAuditDashboard() {
  const [metrics, setMetrics] = useState<SEOMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Run SEO audit
  const runAudit = async () => {
    setIsLoading(true);
    
    try {
      // Simulate audit process (in real implementation, this would call actual audit functions)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const auditResults: SEOMetrics = {
        score: 85,
        issues: [
          {
            type: "warning",
            category: "Images",
            message: "3 images missing alt text",
            element: "img[src='/product-1.jpg']",
            fix: "Add descriptive alt text to improve accessibility and SEO"
          },
          {
            type: "error",
            category: "Meta Tags",
            message: "Missing meta description on 2 pages",
            fix: "Add unique meta descriptions under 160 characters"
          },
          {
            type: "info",
            category: "Performance",
            message: "Consider lazy loading for below-fold images",
            fix: "Implement intersection observer for better performance"
          }
        ],
        recommendations: [
          "Add more internal links between related products",
          "Optimize image file sizes for better Core Web Vitals",
          "Implement breadcrumb navigation on all pages",
          "Add FAQ schema markup to product pages",
          "Optimize font loading with font-display: swap"
        ],
        performance: {
          lcp: 2100,
          fid: 85,
          cls: 0.08,
          fcp: 1200,
          ttfb: 400
        },
        accessibility: {
          score: 92,
          issues: 2,
          warnings: 5
        },
        seo: {
          metaTags: true,
          headingStructure: true,
          imageAltText: false,
          internalLinks: true,
          structuredData: true,
          sitemap: true,
          robotsTxt: true
        }
      };
      
      setMetrics(auditResults);
      setLastUpdated(new Date());
    } catch (error) {
      console.error("Audit failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Run initial audit
  useEffect(() => {
    runAudit();
  }, []);

  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  // Get performance rating
  const getPerformanceRating = (metric: string, value: number | null) => {
    if (value === null) return { rating: "unknown", color: "gray" };
    
    switch (metric) {
      case "lcp":
        return value <= 2500 ? { rating: "good", color: "green" } : 
               value <= 4000 ? { rating: "needs improvement", color: "yellow" } : 
               { rating: "poor", color: "red" };
      case "fid":
        return value <= 100 ? { rating: "good", color: "green" } : 
               value <= 300 ? { rating: "needs improvement", color: "yellow" } : 
               { rating: "poor", color: "red" };
      case "cls":
        return value <= 0.1 ? { rating: "good", color: "green" } : 
               value <= 0.25 ? { rating: "needs improvement", color: "yellow" } : 
               { rating: "poor", color: "red" };
      default:
        return { rating: "unknown", color: "gray" };
    }
  };

  if (!metrics) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Running SEO audit...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">SEO Audit Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Last updated: {lastUpdated?.toLocaleString()}
          </p>
        </div>
        <Button 
          onClick={runAudit} 
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          {isLoading ? 'Running Audit...' : 'Refresh Audit'}
        </Button>
      </div>

      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Overall SEO Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className={`text-6xl font-bold ${getScoreColor(metrics.score)}`}>
              {metrics.score}
            </div>
            <div className="flex-1">
              <div className="w-full bg-gray-200 rounded-full h-4">
                <div 
                  className={`h-4 rounded-full ${
                    metrics.score >= 90 ? 'bg-green-500' : 
                    metrics.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${metrics.score}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                {metrics.score >= 90 ? 'Excellent' : 
                 metrics.score >= 70 ? 'Good' : 'Needs Improvement'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Core Web Vitals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Core Web Vitals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { name: "LCP", value: metrics.performance.lcp, unit: "ms", description: "Largest Contentful Paint" },
              { name: "FID", value: metrics.performance.fid, unit: "ms", description: "First Input Delay" },
              { name: "CLS", value: metrics.performance.cls, unit: "", description: "Cumulative Layout Shift" }
            ].map((metric) => {
              const rating = getPerformanceRating(metric.name.toLowerCase(), metric.value);
              return (
                <div key={metric.name} className="text-center p-4 border rounded-lg">
                  <div className={`text-2xl font-bold text-${rating.color}-600`}>
                    {metric.value !== null ? `${metric.value}${metric.unit}` : 'N/A'}
                  </div>
                  <div className="text-sm font-medium">{metric.name}</div>
                  <div className="text-xs text-gray-500">{metric.description}</div>
                  <Badge 
                    variant={rating.color === 'green' ? 'default' : rating.color === 'yellow' ? 'secondary' : 'destructive'}
                    className="mt-2"
                  >
                    {rating.rating}
                  </Badge>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* SEO Checks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            SEO Checks
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(metrics.seo).map(([key, passed]) => (
              <div key={key} className="flex items-center gap-3 p-3 border rounded-lg">
                {passed ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className="capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Issues */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Issues Found ({metrics.issues.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {metrics.issues.map((issue, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                {issue.type === 'error' && <XCircle className="h-5 w-5 text-red-600 mt-0.5" />}
                {issue.type === 'warning' && <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />}
                {issue.type === 'info' && <Eye className="h-5 w-5 text-blue-600 mt-0.5" />}
                
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline">{issue.category}</Badge>
                    <span className="font-medium">{issue.message}</span>
                  </div>
                  {issue.element && (
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {issue.element}
                    </code>
                  )}
                  {issue.fix && (
                    <p className="text-sm text-gray-600 mt-2">
                      <strong>Fix:</strong> {issue.fix}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {metrics.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                <span>{recommendation}</span>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Test in Search Console
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              View Analytics
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Run Lighthouse
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Check Sitemap
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Export utility functions
export const seoAuditUtils = {
  // Check meta tags
  checkMetaTags: () => {
    const issues: SEOIssue[] = [];
    
    // Check title
    const title = document.querySelector('title');
    if (!title || title.textContent!.length < 30 || title.textContent!.length > 60) {
      issues.push({
        type: 'warning',
        category: 'Meta Tags',
        message: 'Title tag should be 30-60 characters',
        element: 'title',
        fix: 'Optimize title length for better search results'
      });
    }
    
    // Check meta description
    const description = document.querySelector('meta[name="description"]');
    if (!description || description.getAttribute('content')!.length > 160) {
      issues.push({
        type: 'error',
        category: 'Meta Tags',
        message: 'Meta description missing or too long',
        element: 'meta[name="description"]',
        fix: 'Add meta description under 160 characters'
      });
    }
    
    return issues;
  },

  // Check image alt text
  checkImageAltText: () => {
    const issues: SEOIssue[] = [];
    const images = document.querySelectorAll('img');
    
    images.forEach((img, index) => {
      if (!img.alt || img.alt.trim() === '') {
        issues.push({
          type: 'warning',
          category: 'Images',
          message: `Image ${index + 1} missing alt text`,
          element: `img[src="${img.src}"]`,
          fix: 'Add descriptive alt text for accessibility and SEO'
        });
      }
    });
    
    return issues;
  },

  // Check heading structure
  checkHeadingStructure: () => {
    const issues: SEOIssue[] = [];
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    let hasH1 = false;
    let previousLevel = 0;
    
    headings.forEach((heading) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (level === 1) {
        if (hasH1) {
          issues.push({
            type: 'warning',
            category: 'Headings',
            message: 'Multiple H1 tags found',
            element: heading.tagName.toLowerCase(),
            fix: 'Use only one H1 tag per page'
          });
        }
        hasH1 = true;
      }
      
      if (previousLevel > 0 && level > previousLevel + 1) {
        issues.push({
          type: 'warning',
          category: 'Headings',
          message: 'Heading hierarchy skipped',
          element: heading.tagName.toLowerCase(),
          fix: 'Maintain proper heading hierarchy (H1 → H2 → H3...)'
        });
      }
      
      previousLevel = level;
    });
    
    if (!hasH1) {
      issues.push({
        type: 'error',
        category: 'Headings',
        message: 'No H1 tag found',
        fix: 'Add an H1 tag to define the main topic of the page'
      });
    }
    
    return issues;
  },
};
