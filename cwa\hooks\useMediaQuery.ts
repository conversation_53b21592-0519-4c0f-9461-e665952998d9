"use client";

import { useState, useEffect } from "react";

/**
 * Custom hook for responsive design using CSS media queries
 * @param query - CSS media query string
 * @returns boolean indicating if the media query matches
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === "undefined") {
      return;
    }

    const mediaQuery = window.matchMedia(query);
    
    // Set initial value
    setMatches(mediaQuery.matches);

    // Create event listener function
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Add listener
    mediaQuery.addEventListener("change", handleChange);

    // Cleanup function
    return () => {
      mediaQuery.removeEventListener("change", handleChange);
    };
  }, [query]);

  return matches;
}

/**
 * Predefined breakpoint hooks for common screen sizes
 */
export const useBreakpoints = () => {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const isTablet = useMediaQuery("(min-width: 768px) and (max-width: 1023px)");
  const isDesktop = useMediaQuery("(min-width: 1024px) and (max-width: 1439px)");
  const isLargeScreen = useMediaQuery("(min-width: 1440px)");
  const isSmallMobile = useMediaQuery("(max-width: 374px)");
  const isTouchDevice = useMediaQuery("(hover: none) and (pointer: coarse)");

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    isSmallMobile,
    isTouchDevice,
    // Convenience properties
    isTabletOrMobile: isMobile || isTablet,
    isDesktopOrLarger: isDesktop || isLargeScreen,
  };
};
